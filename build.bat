@echo off
echo 正在构建钉钉机器人...

REM 清理旧的构建文件
if exist dingtalk-bot.exe del dingtalk-bot.exe

REM 下载依赖
echo 下载依赖包...
go mod tidy

REM 构建程序
echo 编译程序...
go build -o dingtalk-bot.exe

if exist dingtalk-bot.exe (
    echo 构建成功！
    echo 可执行文件: dingtalk-bot.exe
    echo.
    echo 使用方法:
    echo 1. 确保 .env 文件包含正确的配置信息
    echo 2. 运行: dingtalk-bot.exe
    echo.
) else (
    echo 构建失败！
    pause
    exit /b 1
)

pause
